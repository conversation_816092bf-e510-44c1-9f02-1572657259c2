use duct::cmd;
use lazy_static::lazy_static;
use pretty_assertions::assert_eq;
use serde_json::{json, Value};
use std::sync::{Arc, Mutex};
use std::time::Duration;
use tokio::time::delay_for;

// Test server configuration
const TEST_PORT: &str = "13001";
const TEST_URL_BASE: &str = "http://localhost";

// API responses for success and failure
const LENDABLE: Value = json!([]);
const NOT_LENDABLE: Value = json!(null);

lazy_static! {
    static ref SERVER: Arc<Mutex<duct::ReaderHandle>> = {
        println!("Building for UNFEEABLE tests");
        cmd!("cargo", "run", "--", "../authoritative-rules.lnd")
            .dir("../compiler")
            .run()
            .expect("Being able to compile");

        println!("Linking built for UNFEEABLE tests");
        cmd!("ln", "-sf", "../formulation/", ".")
            .run()
            .expect("Making a link of the formulations");

        println!("Starting server for UNFEEABLE tests");
        Arc::new(Mutex::new(
            cmd!("cargo", "run", "--", TEST_PORT)
                .reader()
                .expect("Valid server"),
        ))
    };
}

async fn wait_for_server() {
    lazy_static::initialize(&SERVER);
    for i in 0..(1000 as usize) {
        if let Ok(status) = reqwest::get(&test_url("/status/shallow"))
            .await
            .map(|x| x.status())
        {
            if status == reqwest::StatusCode::OK {
                break;
            }
        }
        delay_for(Duration::from_millis(10)).await;
    }
}

fn test_url(path: &str) -> String {
    format!("{}:{}{}", TEST_URL_BASE, TEST_PORT, path)
}

async fn post_json(path: &str, json: &Value) -> Value {
    wait_for_server().await;
    reqwest::Client::new()
        .post(&test_url(path))
        .json(json)
        .send()
        .await
        .expect("Valid response")
        .json()
        .await
        .expect("Valid JSON")
}

// Helper function to create fee constraint expectation
fn fee_constraint(min: f64, max: f64) -> Value {
    json!([
        {
            "constraint": "GTE",
            "value": min.to_string()
        },
        {
            "constraint": "LTE", 
            "value": max.to_string()
        }
    ])
}

// Helper function to create loan request
fn create_loan_request(
    province: &str,
    loan_type: &str,
    amount: Option<&str>,
    amount_to_customer: Option<&str>,
    apr: Option<&str>,
    query: &str,
) -> Value {
    let mut loan = json!({
        "country": "us",
        "province": province,
        "type": loan_type,
        "loanInterestType": "pni"
    });

    if let Some(amt) = amount {
        loan["amount"] = json!(amt);
    }
    if let Some(atc) = amount_to_customer {
        loan["amountToCustomer"] = json!(atc);
    }
    if let Some(a) = apr {
        loan["apr"] = json!(a);
    }

    json!({
        "loan": loan,
        "query": query
    })
}

//
// UNFEEABLE Rules Fee Capping Tests
//

#[tokio::test]
async fn test_arizona_fee_cap_500() {
    // Arizona: UNFEEABLE WHEN fee > 500
    // Should cap fee at 500 for any loan
    let response = post_json(
        "/v0/legality",
        &create_loan_request("az", "personal", Some("20000"), Some("20000"), None, "fee"),
    )
    .await;

    assert_eq!(fee_constraint(0.0, 500.0), response);
}

#[tokio::test]
async fn test_arkansas_fee_cap_0_with_amount_to_customer() {
    // Arkansas: UNFEEABLE WHEN fee > 0 AND amountToCustomer > 10k
    // Should cap fee at 0 when amountToCustomer > 10k
    let response = post_json(
        "/v0/legality",
        &create_loan_request("ar", "business", Some("30000"), Some("15000"), None, "fee"),
    )
    .await;

    assert_eq!(fee_constraint(0.0, 0.0), response);
}

#[tokio::test]
async fn test_arkansas_no_fee_cap_with_low_amount_to_customer() {
    // Arkansas: UNFEEABLE WHEN fee > 0 AND amountToCustomer > 10k
    // Should NOT cap fee when amountToCustomer <= 10k
    let response = post_json(
        "/v0/legality",
        &create_loan_request("ar", "business", Some("30000"), Some("8000"), None, "fee"),
    )
    .await;

    // Should return normal fee constraints, not capped at 0
    assert_ne!(fee_constraint(0.0, 0.0), response);
}

#[tokio::test]
async fn test_colorado_fee_cap_100_with_amount_to_customer() {
    // Colorado: UNFEEABLE WHEN fee > 100 AND amountToCustomer > 30k
    // Should cap fee at 100 when amountToCustomer > 30k
    let response = post_json(
        "/v0/legality",
        &create_loan_request("co", "personal", Some("50000"), Some("35000"), None, "fee"),
    )
    .await;

    assert_eq!(fee_constraint(0.0, 100.0), response);
}

#[tokio::test]
async fn test_colorado_no_fee_cap_with_low_amount_to_customer() {
    // Colorado: UNFEEABLE WHEN fee > 100 AND amountToCustomer > 30k
    // Should NOT cap fee when amountToCustomer <= 30k
    let response = post_json(
        "/v0/legality",
        &create_loan_request("co", "personal", Some("50000"), Some("25000"), None, "fee"),
    )
    .await;

    // Should return normal fee constraints, not capped at 100
    assert_ne!(fee_constraint(0.0, 100.0), response);
}

#[tokio::test]
async fn test_georgia_fee_cap_0_with_amount() {
    // Georgia: UNFEEABLE WHEN fee > 0 AND amount > 15k
    // Should cap fee at 0 when amount > 15k
    let response = post_json(
        "/v0/legality",
        &create_loan_request("ga", "business", Some("20000"), Some("20000"), None, "fee"),
    )
    .await;

    assert_eq!(fee_constraint(0.0, 0.0), response);
}

#[tokio::test]
async fn test_georgia_no_fee_cap_with_low_amount() {
    // Georgia: UNFEEABLE WHEN fee > 0 AND amount > 15k
    // Should NOT cap fee when amount <= 15k
    let response = post_json(
        "/v0/legality",
        &create_loan_request("ga", "business", Some("10000"), Some("10000"), None, "fee"),
    )
    .await;

    // Should return normal fee constraints, not capped at 0
    assert_ne!(fee_constraint(0.0, 0.0), response);
}

#[tokio::test]
async fn test_idaho_fee_cap_0_with_amount_to_customer() {
    // Idaho: UNFEEABLE WHEN fee > 0 AND amountToCustomer > 30k
    // Should cap fee at 0 when amountToCustomer > 30k
    let response = post_json(
        "/v0/legality",
        &create_loan_request("id", "business", Some("50000"), Some("35000"), None, "fee"),
    )
    .await;

    assert_eq!(fee_constraint(0.0, 0.0), response);
}

#[tokio::test]
async fn test_kentucky_fee_cap_300_with_low_amount_to_customer() {
    // Kentucky: UNFEEABLE WHEN fee > 300 AND amountToCustomer < 69.5k
    // Should cap fee at 300 when amountToCustomer < 69.5k
    let response = post_json(
        "/v0/legality",
        &create_loan_request("ky", "business", Some("50000"), Some("50000"), None, "fee"),
    )
    .await;

    assert_eq!(fee_constraint(0.0, 300.0), response);
}

#[tokio::test]
async fn test_kentucky_no_fee_cap_with_high_amount_to_customer() {
    // Kentucky: UNFEEABLE WHEN fee > 300 AND amountToCustomer < 69.5k
    // Should NOT cap fee when amountToCustomer >= 69.5k
    let response = post_json(
        "/v0/legality",
        &create_loan_request("ky", "business", Some("100000"), Some("80000"), None, "fee"),
    )
    .await;

    // Should return normal fee constraints, not capped at 300
    assert_ne!(fee_constraint(0.0, 300.0), response);
}

#[tokio::test]
async fn test_missouri_fee_cap_0_with_low_amount_to_customer() {
    // Missouri: UNFEEABLE WHEN fee > 0 AND amountToCustomer < 100k
    // Should cap fee at 0 when amountToCustomer < 100k
    let response = post_json(
        "/v0/legality",
        &create_loan_request("mo", "business", Some("50000"), Some("50000"), None, "fee"),
    )
    .await;

    assert_eq!(fee_constraint(0.0, 0.0), response);
}

#[tokio::test]
async fn test_missouri_no_fee_cap_with_high_amount_to_customer() {
    // Missouri: UNFEEABLE WHEN fee > 0 AND amountToCustomer < 100k
    // Should NOT cap fee when amountToCustomer >= 100k
    let response = post_json(
        "/v0/legality",
        &create_loan_request("mo", "business", Some("150000"), Some("120000"), None, "fee"),
    )
    .await;

    // Should return normal fee constraints, not capped at 0
    assert_ne!(fee_constraint(0.0, 0.0), response);
}

#[tokio::test]
async fn test_new_jersey_fee_cap_0_always() {
    // New Jersey: UNFEEABLE WHEN fee > 0
    // Should always cap fee at 0 regardless of other parameters
    let response = post_json(
        "/v0/legality",
        &create_loan_request("nj", "business", Some("50000"), Some("50000"), None, "fee"),
    )
    .await;

    assert_eq!(fee_constraint(0.0, 0.0), response);
}

//
// UNFEEABLE Rules Legality Tests
//

#[tokio::test]
async fn test_arkansas_legality_with_zero_fee_high_amount_to_customer() {
    // Arkansas: UNFEEABLE WHEN fee > 0 AND amountToCustomer > 10k
    // Should be legal with fee = 0 and amountToCustomer > 10k
    let mut loan_request = create_loan_request("ar", "business", Some("30000"), Some("15000"), None, "legality");
    loan_request["loan"]["fee"] = json!("0");

    let response = post_json("/v0/legality", &loan_request).await;
    assert_eq!(LENDABLE, response);
}

#[tokio::test]
async fn test_arkansas_illegality_with_positive_fee_high_amount_to_customer() {
    // Arkansas: UNFEEABLE WHEN fee > 0 AND amountToCustomer > 10k
    // Should be illegal with fee > 0 and amountToCustomer > 10k
    let mut loan_request = create_loan_request("ar", "business", Some("30000"), Some("15000"), None, "legality");
    loan_request["loan"]["fee"] = json!("100");

    let response = post_json("/v0/legality", &loan_request).await;
    assert_eq!(NOT_LENDABLE, response);
}

#[tokio::test]
async fn test_colorado_legality_with_fee_100_high_amount_to_customer() {
    // Colorado: UNFEEABLE WHEN fee > 100 AND amountToCustomer > 30k
    // Should be legal with fee = 100 and amountToCustomer > 30k
    let mut loan_request = create_loan_request("co", "personal", Some("50000"), Some("35000"), None, "legality");
    loan_request["loan"]["fee"] = json!("100");

    let response = post_json("/v0/legality", &loan_request).await;
    assert_eq!(LENDABLE, response);
}

#[tokio::test]
async fn test_colorado_illegality_with_fee_over_100_high_amount_to_customer() {
    // Colorado: UNFEEABLE WHEN fee > 100 AND amountToCustomer > 30k
    // Should be illegal with fee > 100 and amountToCustomer > 30k
    let mut loan_request = create_loan_request("co", "personal", Some("50000"), Some("35000"), None, "legality");
    loan_request["loan"]["fee"] = json!("150");

    let response = post_json("/v0/legality", &loan_request).await;
    assert_eq!(NOT_LENDABLE, response);
}

#[tokio::test]
async fn test_georgia_legality_with_zero_fee_high_amount() {
    // Georgia: UNFEEABLE WHEN fee > 0 AND amount > 15k
    // Should be legal with fee = 0 and amount > 15k
    let mut loan_request = create_loan_request("ga", "business", Some("20000"), Some("20000"), None, "legality");
    loan_request["loan"]["fee"] = json!("0");

    let response = post_json("/v0/legality", &loan_request).await;
    assert_eq!(LENDABLE, response);
}

#[tokio::test]
async fn test_georgia_illegality_with_positive_fee_high_amount() {
    // Georgia: UNFEEABLE WHEN fee > 0 AND amount > 15k
    // Should be illegal with fee > 0 and amount > 15k
    let mut loan_request = create_loan_request("ga", "business", Some("20000"), Some("20000"), None, "legality");
    loan_request["loan"]["fee"] = json!("50");

    let response = post_json("/v0/legality", &loan_request).await;
    assert_eq!(NOT_LENDABLE, response);
}

#[tokio::test]
async fn test_kentucky_legality_with_fee_300_low_amount_to_customer() {
    // Kentucky: UNFEEABLE WHEN fee > 300 AND amountToCustomer < 69.5k
    // Should be legal with fee = 300 and amountToCustomer < 69.5k
    let mut loan_request = create_loan_request("ky", "business", Some("50000"), Some("50000"), None, "legality");
    loan_request["loan"]["fee"] = json!("300");

    let response = post_json("/v0/legality", &loan_request).await;
    assert_eq!(LENDABLE, response);
}

#[tokio::test]
async fn test_kentucky_illegality_with_fee_over_300_low_amount_to_customer() {
    // Kentucky: UNFEEABLE WHEN fee > 300 AND amountToCustomer < 69.5k
    // Should be illegal with fee > 300 and amountToCustomer < 69.5k
    let mut loan_request = create_loan_request("ky", "business", Some("50000"), Some("50000"), None, "legality");
    loan_request["loan"]["fee"] = json!("400");

    let response = post_json("/v0/legality", &loan_request).await;
    assert_eq!(NOT_LENDABLE, response);
}

#[tokio::test]
async fn test_new_jersey_illegality_with_any_positive_fee() {
    // New Jersey: UNFEEABLE WHEN fee > 0
    // Should be illegal with any fee > 0
    let mut loan_request = create_loan_request("nj", "business", Some("50000"), Some("50000"), None, "legality");
    loan_request["loan"]["fee"] = json!("1");

    let response = post_json("/v0/legality", &loan_request).await;
    assert_eq!(NOT_LENDABLE, response);
}

#[tokio::test]
async fn test_new_jersey_legality_with_zero_fee() {
    // New Jersey: UNFEEABLE WHEN fee > 0
    // Should be legal with fee = 0
    let mut loan_request = create_loan_request("nj", "business", Some("50000"), Some("50000"), None, "legality");
    loan_request["loan"]["fee"] = json!("0");

    let response = post_json("/v0/legality", &loan_request).await;
    assert_eq!(LENDABLE, response);
}

//
// Edge Cases and Boundary Tests
//

#[tokio::test]
async fn test_arkansas_boundary_amount_to_customer_exactly_10k() {
    // Arkansas: UNFEEABLE WHEN fee > 0 AND amountToCustomer > 10k
    // Test boundary condition: amountToCustomer = 10k exactly
    let response = post_json(
        "/v0/legality",
        &create_loan_request("ar", "business", Some("30000"), Some("10000"), None, "fee"),
    )
    .await;

    // Should NOT cap fee when amountToCustomer = 10k (not > 10k)
    assert_ne!(fee_constraint(0.0, 0.0), response);
}

#[tokio::test]
async fn test_arkansas_boundary_amount_to_customer_just_over_10k() {
    // Arkansas: UNFEEABLE WHEN fee > 0 AND amountToCustomer > 10k
    // Test boundary condition: amountToCustomer = 10000.01
    let response = post_json(
        "/v0/legality",
        &create_loan_request("ar", "business", Some("30000"), Some("10000.01"), None, "fee"),
    )
    .await;

    // Should cap fee when amountToCustomer > 10k
    assert_eq!(fee_constraint(0.0, 0.0), response);
}

#[tokio::test]
async fn test_colorado_boundary_amount_to_customer_exactly_30k() {
    // Colorado: UNFEEABLE WHEN fee > 100 AND amountToCustomer > 30k
    // Test boundary condition: amountToCustomer = 30k exactly
    let response = post_json(
        "/v0/legality",
        &create_loan_request("co", "personal", Some("50000"), Some("30000"), None, "fee"),
    )
    .await;

    // Should NOT cap fee when amountToCustomer = 30k (not > 30k)
    assert_ne!(fee_constraint(0.0, 100.0), response);
}

#[tokio::test]
async fn test_georgia_boundary_amount_exactly_15k() {
    // Georgia: UNFEEABLE WHEN fee > 0 AND amount > 15k
    // Test boundary condition: amount = 15k exactly
    let response = post_json(
        "/v0/legality",
        &create_loan_request("ga", "business", Some("15000"), Some("15000"), None, "fee"),
    )
    .await;

    // Should NOT cap fee when amount = 15k (not > 15k)
    assert_ne!(fee_constraint(0.0, 0.0), response);
}

#[tokio::test]
async fn test_kentucky_boundary_amount_to_customer_exactly_69_5k() {
    // Kentucky: UNFEEABLE WHEN fee > 300 AND amountToCustomer < 69.5k
    // Test boundary condition: amountToCustomer = 69.5k exactly
    let response = post_json(
        "/v0/legality",
        &create_loan_request("ky", "business", Some("100000"), Some("69500"), None, "fee"),
    )
    .await;

    // Should NOT cap fee when amountToCustomer = 69.5k (not < 69.5k)
    assert_ne!(fee_constraint(0.0, 300.0), response);
}

#[tokio::test]
async fn test_missouri_boundary_amount_to_customer_exactly_100k() {
    // Missouri: UNFEEABLE WHEN fee > 0 AND amountToCustomer < 100k
    // Test boundary condition: amountToCustomer = 100k exactly
    let response = post_json(
        "/v0/legality",
        &create_loan_request("mo", "business", Some("150000"), Some("100000"), None, "fee"),
    )
    .await;

    // Should NOT cap fee when amountToCustomer = 100k (not < 100k)
    assert_ne!(fee_constraint(0.0, 0.0), response);
}

//
// Maximum Fee Constraint Tests (No UNFEEABLE rules)
//

#[tokio::test]
async fn test_california_no_unfeeable_rule_max_fee() {
    // California has no UNFEEABLE rules, should return normal max fee constraints
    let response = post_json(
        "/v0/legality",
        &create_loan_request("ca", "personal", Some("50000"), Some("50000"), None, "fee"),
    )
    .await;

    // Should return normal fee constraints, not capped by UNFEEABLE rules
    // The exact max will depend on the solver's normal fee constraints
    if let Value::Array(constraints) = &response {
        assert!(constraints.len() >= 1);
        // Should have at least a GTE constraint for minimum fee
        let has_gte = constraints.iter().any(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("GTE")
        });
        assert!(has_gte, "Should have GTE constraint for minimum fee");
    } else {
        panic!("Expected array of constraints, got: {:?}", response);
    }
}

#[tokio::test]
async fn test_florida_no_unfeeable_rule_max_fee() {
    // Florida has no UNFEEABLE rules, should return normal max fee constraints
    let response = post_json(
        "/v0/legality",
        &create_loan_request("fl", "business", Some("100000"), Some("100000"), None, "fee"),
    )
    .await;

    // Should return normal fee constraints, not capped by UNFEEABLE rules
    if let Value::Array(constraints) = &response {
        assert!(constraints.len() >= 1);
        // Should have at least a GTE constraint for minimum fee
        let has_gte = constraints.iter().any(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("GTE")
        });
        assert!(has_gte, "Should have GTE constraint for minimum fee");
    } else {
        panic!("Expected array of constraints, got: {:?}", response);
    }
}

#[tokio::test]
async fn test_texas_no_unfeeable_rule_max_fee() {
    // Texas has no UNFEEABLE rules, should return normal max fee constraints
    let response = post_json(
        "/v0/legality",
        &create_loan_request("tx", "personal", Some("75000"), Some("75000"), None, "fee"),
    )
    .await;

    // Should return normal fee constraints, not capped by UNFEEABLE rules
    if let Value::Array(constraints) = &response {
        assert!(constraints.len() >= 1);
        // Should have at least a GTE constraint for minimum fee
        let has_gte = constraints.iter().any(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("GTE")
        });
        assert!(has_gte, "Should have GTE constraint for minimum fee");
    } else {
        panic!("Expected array of constraints, got: {:?}", response);
    }
}

//
// Cross-Parameter Interaction Tests
//

#[tokio::test]
async fn test_arkansas_personal_vs_business_same_conditions() {
    // Arkansas: UNFEEABLE WHEN fee > 0 AND amountToCustomer > 10k
    // Test that rule applies to both personal and business loans

    let personal_response = post_json(
        "/v0/legality",
        &create_loan_request("ar", "personal", Some("30000"), Some("15000"), None, "fee"),
    )
    .await;

    let business_response = post_json(
        "/v0/legality",
        &create_loan_request("ar", "business", Some("30000"), Some("15000"), None, "fee"),
    )
    .await;

    // Both should have the same fee capping behavior
    assert_eq!(fee_constraint(0.0, 0.0), personal_response);
    assert_eq!(fee_constraint(0.0, 0.0), business_response);
}

#[tokio::test]
async fn test_colorado_personal_vs_business_same_conditions() {
    // Colorado: UNFEEABLE WHEN fee > 100 AND amountToCustomer > 30k
    // Test that rule applies to both personal and business loans

    let personal_response = post_json(
        "/v0/legality",
        &create_loan_request("co", "personal", Some("50000"), Some("35000"), None, "fee"),
    )
    .await;

    let business_response = post_json(
        "/v0/legality",
        &create_loan_request("co", "business", Some("50000"), Some("35000"), None, "fee"),
    )
    .await;

    // Both should have the same fee capping behavior
    assert_eq!(fee_constraint(0.0, 100.0), personal_response);
    assert_eq!(fee_constraint(0.0, 100.0), business_response);
}

//
// Complex Scenario Tests
//

#[tokio::test]
async fn test_multiple_unfeeable_conditions_arkansas_with_apr() {
    // Arkansas: UNFEEABLE WHEN fee > 0 AND amountToCustomer > 10k
    // Also has: ILLEGAL IF apr > 17%
    // Test interaction between UNFEEABLE and other constraints

    let mut loan_request = create_loan_request("ar", "business", Some("30000"), Some("15000"), Some("0.15"), "legality");
    loan_request["loan"]["fee"] = json!("0");

    let response = post_json("/v0/legality", &loan_request).await;
    // Should be legal with fee=0, apr=15%, amountToCustomer=15k
    assert_eq!(LENDABLE, response);

    // Test with fee > 0 - should be illegal due to UNFEEABLE
    loan_request["loan"]["fee"] = json!("50");
    let response = post_json("/v0/legality", &loan_request).await;
    assert_eq!(NOT_LENDABLE, response);

    // Test with high APR - should be illegal due to APR constraint
    loan_request["loan"]["fee"] = json!("0");
    loan_request["loan"]["apr"] = json!("0.20");
    let response = post_json("/v0/legality", &loan_request).await;
    assert_eq!(NOT_LENDABLE, response);
}

#[tokio::test]
async fn test_georgia_amount_vs_amount_to_customer_difference() {
    // Georgia: UNFEEABLE WHEN fee > 0 AND amount > 15k
    // Test that it uses 'amount' not 'amountToCustomer'

    // Case 1: amount > 15k, amountToCustomer <= 15k - should trigger UNFEEABLE
    let response1 = post_json(
        "/v0/legality",
        &create_loan_request("ga", "business", Some("20000"), Some("10000"), None, "fee"),
    )
    .await;
    assert_eq!(fee_constraint(0.0, 0.0), response1);

    // Case 2: amount <= 15k, amountToCustomer > 15k - should NOT trigger UNFEEABLE
    let response2 = post_json(
        "/v0/legality",
        &create_loan_request("ga", "business", Some("10000"), Some("20000"), None, "fee"),
    )
    .await;
    assert_ne!(fee_constraint(0.0, 0.0), response2);
}

//
// Stress Tests with Extreme Values
//

#[tokio::test]
async fn test_arizona_fee_cap_with_very_large_amounts() {
    // Arizona: UNFEEABLE WHEN fee > 500
    // Test with very large loan amounts
    let response = post_json(
        "/v0/legality",
        &create_loan_request("az", "business", Some("10000000"), Some("10000000"), None, "fee"),
    )
    .await;

    // Should still cap at 500 regardless of loan size
    assert_eq!(fee_constraint(0.0, 500.0), response);
}

#[tokio::test]
async fn test_new_jersey_fee_cap_with_minimal_amounts() {
    // New Jersey: UNFEEABLE WHEN fee > 0
    // Test with minimal loan amounts
    let response = post_json(
        "/v0/legality",
        &create_loan_request("nj", "personal", Some("5001"), Some("5001"), None, "fee"),
    )
    .await;

    // Should still cap at 0 regardless of loan size
    assert_eq!(fee_constraint(0.0, 0.0), response);
}

//
// Missing Parameter Tests
//

#[tokio::test]
async fn test_arkansas_missing_amount_to_customer() {
    // Arkansas: UNFEEABLE WHEN fee > 0 AND amountToCustomer > 10k
    // Test behavior when amountToCustomer is not provided
    let response = post_json(
        "/v0/legality",
        &create_loan_request("ar", "business", Some("30000"), None, None, "fee"),
    )
    .await;

    // Should not apply UNFEEABLE rule when amountToCustomer is missing
    assert_ne!(fee_constraint(0.0, 0.0), response);
}

#[tokio::test]
async fn test_georgia_missing_amount() {
    // Georgia: UNFEEABLE WHEN fee > 0 AND amount > 15k
    // Test behavior when amount is not provided
    let response = post_json(
        "/v0/legality",
        &create_loan_request("ga", "business", None, Some("20000"), None, "fee"),
    )
    .await;

    // Should not apply UNFEEABLE rule when amount is missing
    assert_ne!(fee_constraint(0.0, 0.0), response);
}

//
// No Fee Cap Tests - Normal Fee Constraints
//

#[tokio::test]
async fn test_arkansas_no_cap_conditions_met() {
    // Arkansas: UNFEEABLE WHEN fee > 0 AND amountToCustomer > 10k
    // Should NOT cap when amountToCustomer <= 10k
    let response = post_json(
        "/v0/legality",
        &create_loan_request("ar", "business", Some("30000"), Some("8000"), None, "fee"),
    )
    .await;

    // Should return normal fee constraints, not UNFEEABLE cap
    if let Value::Array(constraints) = &response {
        assert!(constraints.len() >= 1);
        // Should have normal fee constraints, not capped at 0
        let max_constraint = constraints.iter().find(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
        });

        if let Some(max_constraint) = max_constraint {
            let max_value = max_constraint.get("value")
                .and_then(|v| v.as_str())
                .and_then(|s| s.parse::<f64>().ok())
                .unwrap_or(0.0);
            // Max fee should be significantly higher than 0 (normal fee constraints)
            assert!(max_value > 100.0, "Expected normal fee constraints, got max fee: {}", max_value);
        }
    } else {
        panic!("Expected array of constraints, got: {:?}", response);
    }
}

#[tokio::test]
async fn test_colorado_no_cap_conditions_met() {
    // Colorado: UNFEEABLE WHEN fee > 100 AND amountToCustomer > 30k
    // Should NOT cap when amountToCustomer <= 30k
    let response = post_json(
        "/v0/legality",
        &create_loan_request("co", "personal", Some("50000"), Some("25000"), None, "fee"),
    )
    .await;

    // Should return normal fee constraints, not UNFEEABLE cap
    if let Value::Array(constraints) = &response {
        assert!(constraints.len() >= 1);
        // Should have normal fee constraints, not capped at 100
        let max_constraint = constraints.iter().find(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
        });

        if let Some(max_constraint) = max_constraint {
            let max_value = max_constraint.get("value")
                .and_then(|v| v.as_str())
                .and_then(|s| s.parse::<f64>().ok())
                .unwrap_or(0.0);
            // Max fee should be significantly higher than 100 (normal fee constraints)
            assert!(max_value > 200.0, "Expected normal fee constraints, got max fee: {}", max_value);
        }
    } else {
        panic!("Expected array of constraints, got: {:?}", response);
    }
}

#[tokio::test]
async fn test_georgia_no_cap_conditions_met() {
    // Georgia: UNFEEABLE WHEN fee > 0 AND amount > 15k
    // Should NOT cap when amount <= 15k
    let response = post_json(
        "/v0/legality",
        &create_loan_request("ga", "business", Some("10000"), Some("10000"), None, "fee"),
    )
    .await;

    // Should return normal fee constraints, not UNFEEABLE cap
    if let Value::Array(constraints) = &response {
        assert!(constraints.len() >= 1);
        // Should have normal fee constraints, not capped at 0
        let max_constraint = constraints.iter().find(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
        });

        if let Some(max_constraint) = max_constraint {
            let max_value = max_constraint.get("value")
                .and_then(|v| v.as_str())
                .and_then(|s| s.parse::<f64>().ok())
                .unwrap_or(0.0);
            // Max fee should be significantly higher than 0 (normal fee constraints)
            assert!(max_value > 100.0, "Expected normal fee constraints, got max fee: {}", max_value);
        }
    } else {
        panic!("Expected array of constraints, got: {:?}", response);
    }
}

#[tokio::test]
async fn test_kentucky_no_cap_conditions_met() {
    // Kentucky: UNFEEABLE WHEN fee > 300 AND amountToCustomer < 69.5k
    // Should NOT cap when amountToCustomer >= 69.5k
    let response = post_json(
        "/v0/legality",
        &create_loan_request("ky", "business", Some("100000"), Some("80000"), None, "fee"),
    )
    .await;

    // Should return normal fee constraints, not UNFEEABLE cap
    if let Value::Array(constraints) = &response {
        assert!(constraints.len() >= 1);
        // Should have normal fee constraints, not capped at 300
        let max_constraint = constraints.iter().find(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
        });

        if let Some(max_constraint) = max_constraint {
            let max_value = max_constraint.get("value")
                .and_then(|v| v.as_str())
                .and_then(|s| s.parse::<f64>().ok())
                .unwrap_or(0.0);
            // Max fee should be significantly higher than 300 (normal fee constraints)
            assert!(max_value > 500.0, "Expected normal fee constraints, got max fee: {}", max_value);
        }
    } else {
        panic!("Expected array of constraints, got: {:?}", response);
    }
}

#[tokio::test]
async fn test_missouri_no_cap_conditions_met() {
    // Missouri: UNFEEABLE WHEN fee > 0 AND amountToCustomer < 100k
    // Should NOT cap when amountToCustomer >= 100k
    let response = post_json(
        "/v0/legality",
        &create_loan_request("mo", "business", Some("150000"), Some("120000"), None, "fee"),
    )
    .await;

    // Should return normal fee constraints, not UNFEEABLE cap
    if let Value::Array(constraints) = &response {
        assert!(constraints.len() >= 1);
        // Should have normal fee constraints, not capped at 0
        let max_constraint = constraints.iter().find(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
        });

        if let Some(max_constraint) = max_constraint {
            let max_value = max_constraint.get("value")
                .and_then(|v| v.as_str())
                .and_then(|s| s.parse::<f64>().ok())
                .unwrap_or(0.0);
            // Max fee should be significantly higher than 0 (normal fee constraints)
            assert!(max_value > 100.0, "Expected normal fee constraints, got max fee: {}", max_value);
        }
    } else {
        panic!("Expected array of constraints, got: {:?}", response);
    }
}

//
// States Without UNFEEABLE Rules - Should Never Cap
//

#[tokio::test]
async fn test_california_no_unfeeable_normal_constraints() {
    // California has no UNFEEABLE rules - should always return normal fee constraints
    let response = post_json(
        "/v0/legality",
        &create_loan_request("ca", "personal", Some("50000"), Some("50000"), None, "fee"),
    )
    .await;

    // Should return normal fee constraints
    if let Value::Array(constraints) = &response {
        assert!(constraints.len() >= 1);

        let has_gte = constraints.iter().any(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("GTE")
        });
        let has_lte = constraints.iter().any(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
        });

        assert!(has_gte, "Should have GTE constraint for minimum fee");
        assert!(has_lte, "Should have LTE constraint for maximum fee");

        // Check that max fee is reasonable (not artificially capped)
        let max_constraint = constraints.iter().find(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
        });

        if let Some(max_constraint) = max_constraint {
            let max_value = max_constraint.get("value")
                .and_then(|v| v.as_str())
                .and_then(|s| s.parse::<f64>().ok())
                .unwrap_or(0.0);
            // Should have a reasonable max fee, not artificially low
            assert!(max_value > 1000.0, "Expected normal fee constraints, got max fee: {}", max_value);
        }
    } else {
        panic!("Expected array of constraints, got: {:?}", response);
    }
}

#[tokio::test]
async fn test_florida_no_unfeeable_normal_constraints() {
    // Florida has no UNFEEABLE rules - should always return normal fee constraints
    let response = post_json(
        "/v0/legality",
        &create_loan_request("fl", "business", Some("100000"), Some("100000"), None, "fee"),
    )
    .await;

    // Should return normal fee constraints
    if let Value::Array(constraints) = &response {
        assert!(constraints.len() >= 1);

        let has_gte = constraints.iter().any(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("GTE")
        });
        let has_lte = constraints.iter().any(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
        });

        assert!(has_gte, "Should have GTE constraint for minimum fee");
        assert!(has_lte, "Should have LTE constraint for maximum fee");

        // Check that max fee is reasonable (not artificially capped)
        let max_constraint = constraints.iter().find(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
        });

        if let Some(max_constraint) = max_constraint {
            let max_value = max_constraint.get("value")
                .and_then(|v| v.as_str())
                .and_then(|s| s.parse::<f64>().ok())
                .unwrap_or(0.0);
            // Should have a reasonable max fee, not artificially low
            assert!(max_value > 1000.0, "Expected normal fee constraints, got max fee: {}", max_value);
        }
    } else {
        panic!("Expected array of constraints, got: {:?}", response);
    }
}

#[tokio::test]
async fn test_texas_no_unfeeable_normal_constraints() {
    // Texas has no UNFEEABLE rules - should always return normal fee constraints
    let response = post_json(
        "/v0/legality",
        &create_loan_request("tx", "personal", Some("75000"), Some("75000"), None, "fee"),
    )
    .await;

    // Should return normal fee constraints
    if let Value::Array(constraints) = &response {
        assert!(constraints.len() >= 1);

        let has_gte = constraints.iter().any(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("GTE")
        });
        let has_lte = constraints.iter().any(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
        });

        assert!(has_gte, "Should have GTE constraint for minimum fee");
        assert!(has_lte, "Should have LTE constraint for maximum fee");

        // Check that max fee is reasonable (not artificially capped)
        let max_constraint = constraints.iter().find(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
        });

        if let Some(max_constraint) = max_constraint {
            let max_value = max_constraint.get("value")
                .and_then(|v| v.as_str())
                .and_then(|s| s.parse::<f64>().ok())
                .unwrap_or(0.0);
            // Should have a reasonable max fee, not artificially low
            assert!(max_value > 1000.0, "Expected normal fee constraints, got max fee: {}", max_value);
        }
    } else {
        panic!("Expected array of constraints, got: {:?}", response);
    }
}

#[tokio::test]
async fn test_new_york_no_unfeeable_normal_constraints() {
    // New York has no UNFEEABLE rules - should always return normal fee constraints
    let response = post_json(
        "/v0/legality",
        &create_loan_request("ny", "business", Some("200000"), Some("200000"), None, "fee"),
    )
    .await;

    // Should return normal fee constraints
    if let Value::Array(constraints) = &response {
        assert!(constraints.len() >= 1);

        let has_gte = constraints.iter().any(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("GTE")
        });
        let has_lte = constraints.iter().any(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
        });

        assert!(has_gte, "Should have GTE constraint for minimum fee");
        assert!(has_lte, "Should have LTE constraint for maximum fee");

        // Check that max fee is reasonable (not artificially capped)
        let max_constraint = constraints.iter().find(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
        });

        if let Some(max_constraint) = max_constraint {
            let max_value = max_constraint.get("value")
                .and_then(|v| v.as_str())
                .and_then(|s| s.parse::<f64>().ok())
                .unwrap_or(0.0);
            // Should have a reasonable max fee, not artificially low
            assert!(max_value > 1000.0, "Expected normal fee constraints, got max fee: {}", max_value);
        }
    } else {
        panic!("Expected array of constraints, got: {:?}", response);
    }
}

//
// Comparative Tests - Cap vs No Cap
//

#[tokio::test]
async fn test_arkansas_cap_vs_no_cap_comparison() {
    // Arkansas: UNFEEABLE WHEN fee > 0 AND amountToCustomer > 10k

    // Case 1: Should cap (amountToCustomer > 10k)
    let response_cap = post_json(
        "/v0/legality",
        &create_loan_request("ar", "business", Some("30000"), Some("15000"), None, "fee"),
    )
    .await;

    // Case 2: Should NOT cap (amountToCustomer <= 10k)
    let response_no_cap = post_json(
        "/v0/legality",
        &create_loan_request("ar", "business", Some("30000"), Some("8000"), None, "fee"),
    )
    .await;

    // Verify they are different
    assert_ne!(response_cap, response_no_cap);

    // Verify cap case has fee constraint at 0
    assert_eq!(fee_constraint(0.0, 0.0), response_cap);

    // Verify no-cap case has normal fee constraints (much higher max)
    if let Value::Array(constraints) = &response_no_cap {
        let max_constraint = constraints.iter().find(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
        });

        if let Some(max_constraint) = max_constraint {
            let max_value = max_constraint.get("value")
                .and_then(|v| v.as_str())
                .and_then(|s| s.parse::<f64>().ok())
                .unwrap_or(0.0);
            assert!(max_value > 100.0, "Expected normal fee constraints, got max fee: {}", max_value);
        }
    }
}

#[tokio::test]
async fn test_colorado_cap_vs_no_cap_comparison() {
    // Colorado: UNFEEABLE WHEN fee > 100 AND amountToCustomer > 30k

    // Case 1: Should cap (amountToCustomer > 30k)
    let response_cap = post_json(
        "/v0/legality",
        &create_loan_request("co", "personal", Some("50000"), Some("35000"), None, "fee"),
    )
    .await;

    // Case 2: Should NOT cap (amountToCustomer <= 30k)
    let response_no_cap = post_json(
        "/v0/legality",
        &create_loan_request("co", "personal", Some("50000"), Some("25000"), None, "fee"),
    )
    .await;

    // Verify they are different
    assert_ne!(response_cap, response_no_cap);

    // Verify cap case has fee constraint at 100
    assert_eq!(fee_constraint(0.0, 100.0), response_cap);

    // Verify no-cap case has normal fee constraints (much higher max)
    if let Value::Array(constraints) = &response_no_cap {
        let max_constraint = constraints.iter().find(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
        });

        if let Some(max_constraint) = max_constraint {
            let max_value = max_constraint.get("value")
                .and_then(|v| v.as_str())
                .and_then(|s| s.parse::<f64>().ok())
                .unwrap_or(0.0);
            assert!(max_value > 200.0, "Expected normal fee constraints, got max fee: {}", max_value);
        }
    }
}

//
// No Cap with High Fee Values
//

#[tokio::test]
async fn test_no_cap_allows_high_fees() {
    // Test that when UNFEEABLE conditions are NOT met, high fees are allowed
    // California (no UNFEEABLE rules) should allow high fees
    let response = post_json(
        "/v0/legality",
        &create_loan_request("ca", "business", Some("1000000"), Some("1000000"), None, "fee"),
    )
    .await;

    if let Value::Array(constraints) = &response {
        let max_constraint = constraints.iter().find(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
        });

        if let Some(max_constraint) = max_constraint {
            let max_value = max_constraint.get("value")
                .and_then(|v| v.as_str())
                .and_then(|s| s.parse::<f64>().ok())
                .unwrap_or(0.0);
            // Should allow fees much higher than any UNFEEABLE cap
            assert!(max_value > 1000.0, "Expected high fee allowance, got max fee: {}", max_value);
        }
    } else {
        panic!("Expected array of constraints, got: {:?}", response);
    }
}

#[tokio::test]
async fn test_arkansas_no_cap_allows_high_fees() {
    // Arkansas with amountToCustomer <= 10k should allow high fees
    let response = post_json(
        "/v0/legality",
        &create_loan_request("ar", "business", Some("100000"), Some("5000"), None, "fee"),
    )
    .await;

    if let Value::Array(constraints) = &response {
        let max_constraint = constraints.iter().find(|c| {
            c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
        });

        if let Some(max_constraint) = max_constraint {
            let max_value = max_constraint.get("value")
                .and_then(|v| v.as_str())
                .and_then(|s| s.parse::<f64>().ok())
                .unwrap_or(0.0);
            // Should allow fees much higher than the UNFEEABLE cap of 0
            assert!(max_value > 500.0, "Expected high fee allowance, got max fee: {}", max_value);
        }
    } else {
        panic!("Expected array of constraints, got: {:?}", response);
    }
}

//
// Verification Tests - Ensure No False Positives
//

#[tokio::test]
async fn test_no_false_positive_caps() {
    // Test multiple states that should NOT have fee caps to ensure
    // the determine_fee_cap function doesn't create false positives

    let test_cases = vec![
        ("ca", "personal", "50000", "50000"),   // California - no UNFEEABLE rules
        ("fl", "business", "75000", "75000"),   // Florida - no UNFEEABLE rules
        ("tx", "personal", "100000", "100000"), // Texas - no UNFEEABLE rules
        ("ar", "business", "30000", "5000"),    // Arkansas - conditions not met
        ("co", "personal", "50000", "20000"),   // Colorado - conditions not met
        ("ga", "business", "10000", "10000"),   // Georgia - conditions not met
    ];

    for (state, loan_type, amount, amount_to_customer) in test_cases {
        let response = post_json(
            "/v0/legality",
            &create_loan_request(state, loan_type, Some(amount), Some(amount_to_customer), None, "fee"),
        )
        .await;

        if let Value::Array(constraints) = &response {
            let max_constraint = constraints.iter().find(|c| {
                c.get("constraint").and_then(|v| v.as_str()) == Some("LTE")
            });

            if let Some(max_constraint) = max_constraint {
                let max_value = max_constraint.get("value")
                    .and_then(|v| v.as_str())
                    .and_then(|s| s.parse::<f64>().ok())
                    .unwrap_or(0.0);
                // Should have reasonable max fee, not artificially low caps
                assert!(max_value > 100.0,
                    "State {} should not have low fee cap, got max fee: {}", state, max_value);
            }
        } else {
            panic!("Expected array of constraints for state {}, got: {:?}", state, response);
        }
    }
}
