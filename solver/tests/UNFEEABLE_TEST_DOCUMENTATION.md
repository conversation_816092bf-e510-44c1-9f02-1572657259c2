# UNFEEABLE Rules Test Suite Documentation

This document describes the comprehensive test suite for UNFEEABLE rules with `amountToCustomer` and `amount` parameters, including fee capping logic and legality queries.

## Overview

The test suite in `unfeeable_tests.rs` covers all UNFEEABLE rules defined in `authoritative-rules.lnd` and validates:

1. **Fee Capping Logic**: Ensures fees are properly capped based on UNFEEABLE rule thresholds
2. **Legality Queries**: Verifies that loans are correctly marked as legal/illegal based on UNFEEABLE conditions
3. **Edge Cases**: Tests boundary conditions and parameter interactions
4. **Maximum Fee Constraints**: Tests behavior when no UNFEEABLE rules apply

## UNFEEABLE Rules Covered

### 1. Arizona (az)
- **Rule**: `UNFEEABLE WHEN fee > 500`
- **Expected Behavior**: Fee capped at 500 for all loans
- **Tests**: `test_arizona_fee_cap_500`, `test_arizona_fee_cap_with_very_large_amounts`

### 2. Arkansas (ar)
- **Rule**: `UNFEEABLE WHEN fee > 0 AND amountToCustomer > 10k`
- **Expected Behavior**: Fee capped at 0 when amountToCustomer > 10k
- **Tests**: 
  - `test_arkansas_fee_cap_0_with_amount_to_customer`
  - `test_arkansas_no_fee_cap_with_low_amount_to_customer`
  - `test_arkansas_boundary_amount_to_customer_exactly_10k`
  - `test_arkansas_boundary_amount_to_customer_just_over_10k`

### 3. Colorado (co)
- **Rule**: `UNFEEABLE WHEN fee > 100 AND amountToCustomer > 30k`
- **Expected Behavior**: Fee capped at 100 when amountToCustomer > 30k
- **Tests**:
  - `test_colorado_fee_cap_100_with_amount_to_customer`
  - `test_colorado_no_fee_cap_with_low_amount_to_customer`
  - `test_colorado_boundary_amount_to_customer_exactly_30k`

### 4. Georgia (ga)
- **Rule**: `UNFEEABLE WHEN fee > 0 AND amount > 15k`
- **Expected Behavior**: Fee capped at 0 when amount > 15k (note: uses `amount`, not `amountToCustomer`)
- **Tests**:
  - `test_georgia_fee_cap_0_with_amount`
  - `test_georgia_no_fee_cap_with_low_amount`
  - `test_georgia_boundary_amount_exactly_15k`
  - `test_georgia_amount_vs_amount_to_customer_difference`

### 5. Idaho (id)
- **Rule**: `UNFEEABLE WHEN fee > 0 AND amountToCustomer > 30k`
- **Expected Behavior**: Fee capped at 0 when amountToCustomer > 30k
- **Tests**: `test_idaho_fee_cap_0_with_amount_to_customer`

### 6. Kentucky (ky)
- **Rule**: `UNFEEABLE WHEN fee > 300 AND amountToCustomer < 69.5k`
- **Expected Behavior**: Fee capped at 300 when amountToCustomer < 69.5k
- **Tests**:
  - `test_kentucky_fee_cap_300_with_low_amount_to_customer`
  - `test_kentucky_no_fee_cap_with_high_amount_to_customer`
  - `test_kentucky_boundary_amount_to_customer_exactly_69_5k`

### 7. Missouri (mo)
- **Rule**: `UNFEEABLE WHEN fee > 0 AND amountToCustomer < 100k`
- **Expected Behavior**: Fee capped at 0 when amountToCustomer < 100k
- **Tests**:
  - `test_missouri_fee_cap_0_with_low_amount_to_customer`
  - `test_missouri_no_fee_cap_with_high_amount_to_customer`
  - `test_missouri_boundary_amount_to_customer_exactly_100k`

### 8. New Jersey (nj)
- **Rule**: `UNFEEABLE WHEN fee > 0`
- **Expected Behavior**: Fee always capped at 0 regardless of other parameters
- **Tests**:
  - `test_new_jersey_fee_cap_0_always`
  - `test_new_jersey_fee_cap_with_minimal_amounts`

## Test Categories

### Fee Capping Tests
These tests verify that the `determine_fee_cap` function correctly identifies UNFEEABLE conditions and applies appropriate fee caps:

- Tests for each threshold: 0, 100, 300, 500
- Boundary condition testing
- Cross-parameter interaction testing

### Legality Query Tests
These tests verify that loans are correctly marked as legal/illegal when specific fees are provided:

- Legal cases: Fee at or below the threshold
- Illegal cases: Fee above the threshold
- Zero fee cases (should always be legal for fee-based UNFEEABLE rules)

### Edge Case Tests
- **Boundary Conditions**: Test exact threshold values (e.g., amountToCustomer = 10k exactly)
- **Missing Parameters**: Test behavior when required parameters are not provided
- **Cross-Parameter Interactions**: Test that rules use the correct parameter (amount vs amountToCustomer)
- **Loan Type Variations**: Verify rules apply to both personal and business loans

### Maximum Fee Constraint Tests
These tests verify behavior for states without UNFEEABLE rules:

- California, Florida, Texas (no UNFEEABLE rules)
- Should return normal fee constraints from the solver
- Should not be artificially capped by UNFEEABLE logic

## Key Test Patterns

### Fee Constraint Assertion
```rust
assert_eq!(fee_constraint(0.0, 500.0), response);
```
Verifies that the response contains GTE(0) and LTE(500) constraints.

### Legality Assertion
```rust
assert_eq!(LENDABLE, response);    // Legal loan
assert_eq!(NOT_LENDABLE, response); // Illegal loan
```

### Boundary Testing Pattern
```rust
// Test exactly at threshold
create_loan_request("ar", "business", Some("30000"), Some("10000"), None, "fee")
// Test just over threshold  
create_loan_request("ar", "business", Some("30000"), Some("10000.01"), None, "fee")
```

## Running the Tests

```bash
# Run all UNFEEABLE tests
cargo test unfeeable_tests

# Run specific test
cargo test test_arkansas_fee_cap_0_with_amount_to_customer

# Run with output
cargo test unfeeable_tests -- --nocapture
```

## Expected Fee Caps by State

| State | UNFEEABLE Rule | Expected Fee Cap | Condition |
|-------|----------------|------------------|-----------|
| AZ | fee > 500 | 500 | Always |
| AR | fee > 0 AND amountToCustomer > 10k | 0 | When amountToCustomer > 10k |
| CO | fee > 100 AND amountToCustomer > 30k | 100 | When amountToCustomer > 30k |
| GA | fee > 0 AND amount > 15k | 0 | When amount > 15k |
| ID | fee > 0 AND amountToCustomer > 30k | 0 | When amountToCustomer > 30k |
| KY | fee > 300 AND amountToCustomer < 69.5k | 300 | When amountToCustomer < 69.5k |
| MO | fee > 0 AND amountToCustomer < 100k | 0 | When amountToCustomer < 100k |
| NJ | fee > 0 | 0 | Always |

## Business Logic Validation

The tests validate the following business rules:

1. **Fee Capping Priority**: UNFEEABLE rules take precedence over normal fee constraints
2. **Threshold Accuracy**: Exact boundary conditions are properly handled
3. **Parameter Specificity**: Rules correctly distinguish between `amount` and `amountToCustomer`
4. **Universal Application**: Rules apply to both personal and business loans unless specified otherwise
5. **Fallback Behavior**: States without UNFEEABLE rules use normal fee constraint logic
